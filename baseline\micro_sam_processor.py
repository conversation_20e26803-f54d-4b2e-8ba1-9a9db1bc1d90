"""
MicroSAM processor using the universal baseline framework.
"""

import numpy as np
from typing import <PERSON>ple, Optional
from micro_sam import automatic_segmentation

from .base_processor import BaseProcessor, create_hydra_main


class MicroSAMProcessor(BaseProcessor):
    """
    MicroSAM processor for 3D segmentation.
    """
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 chunk_size: Optional[Tuple[int, int, int]] = None,
                 overlap: Optional[Tuple[int, int, int]] = None,
                 target_resolution: Optional[Tuple[int, int, int]] = None,
                 file_extension: str = ".zst",
                 # MicroSAM-specific parameters
                 model_name: str = "vit_b_em_organelles"):
        """
        Initialize MicroSAM processor.
        
        Args:
            input_dir: Directory containing input volumes
            output_dir: Directory to save results
            chunk_size: Size of chunks for processing. Default: (128, 1024, 1024)
            overlap: Overlap between chunks. Default: (16, 128, 128)
            target_resolution: Target resolution for resizing. If None, keep original
            file_extension: File extension to look for
            model_name: MicroSAM model name
        """
        # Set default chunk size and overlap for MicroSAM
        if chunk_size is None:
            chunk_size = (128, 1024, 1024)
        if overlap is None:
            overlap = (16, 128, 128)
            
        self.model_name = model_name
        
        super().__init__(input_dir, output_dir, chunk_size, overlap, target_resolution, file_extension)
    
    def setup_method(self):
        """
        Initialize MicroSAM model.
        """
        try:
            self.predictor, self.segmenter = automatic_segmentation.get_predictor_and_segmenter(
                self.model_name
            )
            print(f"Initialized MicroSAM model: {self.model_name}")
        except Exception as e:
            print(f"Error initializing MicroSAM: {str(e)}")
            raise
    
    def preprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Preprocess volume for MicroSAM.
        
        Args:
            volume: Input volume
            
        Returns:
            Preprocessed volume
        """
        # MicroSAM typically works with normalized float32 data
        if volume.dtype != np.float32:
            volume = volume.astype(np.float32)
            
        # Normalize to 0-1 range if needed
        if volume.max() > 1.0:
            volume = volume / volume.max()
        
        return volume
    
    def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
        """
        Process a single chunk with MicroSAM.
        
        Args:
            chunk: Input volume chunk
            
        Returns:
            Segmentation mask
        """
        try:
            # Run MicroSAM automatic segmentation
            mask_volume = automatic_segmentation.automatic_instance_segmentation(
                predictor=self.predictor,
                segmenter=self.segmenter,
                input_path=chunk,
            )
            
            return mask_volume.astype(np.uint16)
            
        except Exception as e:
            print(f"Error processing chunk: {str(e)}")
            # Return empty mask with same shape
            return np.zeros(chunk.shape, dtype=np.uint16)
    
    def postprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Postprocess the merged volume.
        
        Args:
            volume: Merged volume
            
        Returns:
            Final processed volume
        """
        # Ensure output is uint16
        if volume.dtype != np.uint16:
            volume = volume.astype(np.uint16)
        
        return volume


# Create the main function for this processor
main = create_hydra_main(MicroSAMProcessor)


if __name__ == "__main__":
    main()
