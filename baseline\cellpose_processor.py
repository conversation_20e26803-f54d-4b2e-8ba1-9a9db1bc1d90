"""
Cellpose processor using the universal baseline framework.
"""

import numpy as np
from cellpose import models, core, io
from typing import <PERSON>ple, Optional

from .base_processor import BaseProcessor, create_hydra_main


class CellposeProcessor(BaseProcessor):
    """
    Cellpose processor for 3D segmentation.
    """
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 chunk_size: Optional[Tuple[int, int, int]] = None,
                 overlap: Optional[Tuple[int, int, int]] = None,
                 target_resolution: Optional[Tuple[int, int, int]] = None,
                 file_extension: str = ".zst",
                 # Cellpose-specific parameters
                 model_type: str = "cyto3",
                 batch_size: int = 32,
                 do_3D: bool = True,
                 flow3D_smooth: int = 1,
                 niter: int = 1000,
                 diameter: Optional[float] = None):
        """
        Initialize Cellpose processor.
        
        Args:
            input_dir: Directory containing input volumes
            output_dir: Directory to save results
            chunk_size: Size of chunks for processing. Default: (256, 512, 512)
            overlap: Overlap between chunks. Default: (32, 64, 64)
            target_resolution: Target resolution for resizing. If None, keep original
            file_extension: File extension to look for
            model_type: Cellpose model type
            batch_size: Batch size for processing
            do_3D: Whether to use 3D processing
            flow3D_smooth: 3D flow smoothing parameter
            niter: Number of iterations
            diameter: Cell diameter (None for automatic)
        """
        # Set default chunk size and overlap for Cellpose
        if chunk_size is None:
            chunk_size = (256, 512, 512)
        if overlap is None:
            overlap = (32, 64, 64)
            
        self.model_type = model_type
        self.batch_size = batch_size
        self.do_3D = do_3D
        self.flow3D_smooth = flow3D_smooth
        self.niter = niter
        self.diameter = diameter
        
        super().__init__(input_dir, output_dir, chunk_size, overlap, target_resolution, file_extension)
    
    def setup_method(self):
        """
        Initialize Cellpose model.
        """
        # Setup logger
        io.logger_setup()
        
        # Check GPU availability
        self.use_gpu = core.use_gpu()
        if not self.use_gpu:
            print("Warning: No GPU detected, using CPU")
        
        # Initialize Cellpose model
        self.model = models.CellposeModel(gpu=self.use_gpu, model_type=self.model_type)
        print(f"Initialized Cellpose model: {self.model_type}")
    
    def preprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Preprocess volume for Cellpose.
        
        Args:
            volume: Input volume
            
        Returns:
            Preprocessed volume
        """
        # Ensure volume is in the right format for Cellpose
        if volume.dtype != np.uint8 and volume.dtype != np.uint16:
            # Normalize to 0-255 range
            volume = volume.astype(np.float32)
            volume = (volume - volume.min()) / (volume.max() - volume.min()) * 255
            volume = volume.astype(np.uint8)
        
        return volume
    
    def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
        """
        Process a single chunk with Cellpose.
        
        Args:
            chunk: Input volume chunk
            
        Returns:
            Segmentation mask
        """
        try:
            # Run Cellpose segmentation
            masks, flows, _ = self.model.eval(
                chunk,
                z_axis=0,
                channel_axis=None,  # grayscale
                batch_size=self.batch_size,
                do_3D=self.do_3D,
                flow3D_smooth=self.flow3D_smooth,
                niter=self.niter,
                diameter=self.diameter,
            )
            
            return masks.astype(np.uint16)
            
        except Exception as e:
            print(f"Error processing chunk: {str(e)}")
            # Return empty mask with same shape
            return np.zeros(chunk.shape, dtype=np.uint16)
    
    def postprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Postprocess the merged volume.
        
        Args:
            volume: Merged volume
            
        Returns:
            Final processed volume
        """
        # Ensure output is uint16
        if volume.dtype != np.uint16:
            volume = volume.astype(np.uint16)
        
        return volume


# Create the main function for this processor
main = create_hydra_main(CellposeProcessor)


if __name__ == "__main__":
    main()
