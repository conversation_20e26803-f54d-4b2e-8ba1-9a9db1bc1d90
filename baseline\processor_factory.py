"""
Factory for creating baseline processors.
"""

from typing import Dict, Type, Any, Optional, Tuple
import hydra
from omegaconf import DictConfig

from .base_processor import BaseProcessor
from .cellpose_processor import CellposeProcessor
from .micro_sam_processor import MicroSAMProcessor


class ProcessorFactory:
    """
    Factory class for creating baseline processors.
    """
    
    # Registry of available processors
    PROCESSORS: Dict[str, Type[BaseProcessor]] = {
        'cellpose': CellposeProcessor,
        'micro_sam': MicroSAMProcessor,
        'microsam': MicroSAMProcessor,  # Alias
    }
    
    @classmethod
    def register_processor(cls, name: str, processor_class: Type[BaseProcessor]):
        """
        Register a new processor.
        
        Args:
            name: Name of the processor
            processor_class: Processor class
        """
        cls.PROCESSORS[name] = processor_class
    
    @classmethod
    def create_processor(cls, 
                        processor_name: str,
                        input_dir: str,
                        output_dir: str,
                        chunk_size: Optional[Tuple[int, int, int]] = None,
                        overlap: Optional[Tuple[int, int, int]] = None,
                        target_resolution: Optional[Tuple[int, int, int]] = None,
                        file_extension: str = ".zst",
                        **kwargs) -> BaseProcessor:
        """
        Create a processor instance.
        
        Args:
            processor_name: Name of the processor to create
            input_dir: Input directory
            output_dir: Output directory
            chunk_size: Chunk size for processing
            overlap: Overlap between chunks
            target_resolution: Target resolution
            file_extension: File extension
            **kwargs: Additional processor-specific arguments
            
        Returns:
            Processor instance
        """
        if processor_name not in cls.PROCESSORS:
            available = ', '.join(cls.PROCESSORS.keys())
            raise ValueError(f"Unknown processor: {processor_name}. Available: {available}")
        
        processor_class = cls.PROCESSORS[processor_name]
        
        return processor_class(
            input_dir=input_dir,
            output_dir=output_dir,
            chunk_size=chunk_size,
            overlap=overlap,
            target_resolution=target_resolution,
            file_extension=file_extension,
            **kwargs
        )
    
    @classmethod
    def list_processors(cls) -> list:
        """
        List all available processors.
        
        Returns:
            List of processor names
        """
        return list(cls.PROCESSORS.keys())


def run_baseline(processor_name: str,
                input_dir: str,
                output_dir: str,
                chunk_size: Optional[Tuple[int, int, int]] = None,
                overlap: Optional[Tuple[int, int, int]] = None,
                target_resolution: Optional[Tuple[int, int, int]] = None,
                file_extension: str = ".zst",
                **kwargs):
    """
    Convenient function to run a baseline method.
    
    Args:
        processor_name: Name of the processor
        input_dir: Input directory
        output_dir: Output directory
        chunk_size: Chunk size for processing
        overlap: Overlap between chunks
        target_resolution: Target resolution
        file_extension: File extension
        **kwargs: Additional processor-specific arguments
    """
    processor = ProcessorFactory.create_processor(
        processor_name=processor_name,
        input_dir=input_dir,
        output_dir=output_dir,
        chunk_size=chunk_size,
        overlap=overlap,
        target_resolution=target_resolution,
        file_extension=file_extension,
        **kwargs
    )
    
    processor.process_all()


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg: DictConfig):
    """
    Main function for running baseline methods via command line.
    
    Usage:
        python processor_factory.py processor=cellpose
        python processor_factory.py processor=micro_sam chunk_size=[128,512,512]
    """
    # Get processor name from config
    processor_name = cfg.get('processor', 'cellpose')
    
    # Default paths
    input_dir = cfg.get('input_dir', f"{cfg.datasets_root}/em_s0/val")
    output_dir = cfg.get('output_dir', f"{cfg.output_root}/baseline/{processor_name}")
    
    # Processing parameters
    chunk_size = cfg.get('chunk_size', None)
    overlap = cfg.get('overlap', None)
    target_resolution = cfg.get('target_resolution', None)
    file_extension = cfg.get('file_extension', '.zst')
    
    # Convert lists to tuples if needed
    if chunk_size is not None and isinstance(chunk_size, list):
        chunk_size = tuple(chunk_size)
    if overlap is not None and isinstance(overlap, list):
        overlap = tuple(overlap)
    if target_resolution is not None and isinstance(target_resolution, list):
        target_resolution = tuple(target_resolution)
    
    # Get additional processor-specific parameters
    processor_params = {}
    if processor_name == 'cellpose':
        processor_params.update({
            'model_type': cfg.get('model_type', 'cyto3'),
            'batch_size': cfg.get('batch_size', 32),
            'do_3D': cfg.get('do_3D', True),
            'flow3D_smooth': cfg.get('flow3D_smooth', 1),
            'niter': cfg.get('niter', 1000),
            'diameter': cfg.get('diameter', None),
        })
    elif processor_name in ['micro_sam', 'microsam']:
        processor_params.update({
            'model_name': cfg.get('model_name', 'vit_b_em_organelles'),
        })
    
    print(f"Running {processor_name} baseline...")
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Chunk size: {chunk_size}")
    print(f"Overlap: {overlap}")
    print(f"Target resolution: {target_resolution}")
    print(f"Additional parameters: {processor_params}")
    
    # Run the baseline
    run_baseline(
        processor_name=processor_name,
        input_dir=input_dir,
        output_dir=output_dir,
        chunk_size=chunk_size,
        overlap=overlap,
        target_resolution=target_resolution,
        file_extension=file_extension,
        **processor_params
    )
    
    print(f"Finished processing with {processor_name}")


if __name__ == "__main__":
    main()
