"""
Test script for the universal baseline framework.

This script tests the framework with dummy data to ensure everything works correctly.
"""

import os
import numpy as np
import tempfile
import shutil
from dataprocess.volume import Volume
from processor_factory import ProcessorFactory, run_baseline


def create_test_data(test_dir: str, num_files: int = 2, volume_shape: tuple = (64, 128, 128)):
    """
    Create test data for framework testing.
    
    Args:
        test_dir: Directory to create test data in
        num_files: Number of test files to create
        volume_shape: Shape of test volumes
    """
    os.makedirs(test_dir, exist_ok=True)
    
    for i in range(num_files):
        # Create random volume data
        volume_data = np.random.randint(0, 256, volume_shape, dtype=np.uint8)
        
        # Save as Volume
        volume = Volume(None)
        volume.volume = volume_data
        
        test_file = os.path.join(test_dir, f"test_volume_{i:02d}.zst")
        volume.save_volume(test_file)
        
        print(f"Created test file: {test_file} with shape {volume_shape}")


class DummyProcessor:
    """
    Dummy processor for testing without external dependencies.
    """
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 chunk_size=None,
                 overlap=None,
                 target_resolution=None,
                 file_extension=".zst",
                 **kwargs):
        
        from base_processor import BaseProcessor
        
        class TestProcessor(BaseProcessor):
            def setup_method(self):
                print("Setting up dummy test processor...")
            
            def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
                # Simple processing: threshold and label
                binary = (chunk > 128).astype(np.uint8)
                from scipy import ndimage
                labeled, _ = ndimage.label(binary)
                return labeled.astype(np.uint16)
        
        self.processor = TestProcessor(
            input_dir=input_dir,
            output_dir=output_dir,
            chunk_size=chunk_size,
            overlap=overlap,
            target_resolution=target_resolution,
            file_extension=file_extension
        )
    
    def process_all(self):
        return self.processor.process_all()


def test_framework():
    """
    Test the framework with dummy data.
    """
    print("Testing Universal Baseline Framework...")
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = os.path.join(temp_dir, "input")
        output_dir = os.path.join(temp_dir, "output")
        
        # Create test data
        print("\n1. Creating test data...")
        create_test_data(input_dir, num_files=3, volume_shape=(32, 64, 64))
        
        # Test 1: Basic functionality with dummy processor
        print("\n2. Testing basic functionality...")
        ProcessorFactory.register_processor('dummy', DummyProcessor)
        
        try:
            dummy_processor = DummyProcessor(
                input_dir=input_dir,
                output_dir=os.path.join(output_dir, "dummy"),
                chunk_size=(16, 32, 32),
                overlap=(4, 8, 8)
            )
            dummy_processor.process_all()
            print("✓ Basic functionality test passed")
        except Exception as e:
            print(f"✗ Basic functionality test failed: {e}")
            return False
        
        # Test 2: Factory pattern
        print("\n3. Testing factory pattern...")
        try:
            run_baseline(
                processor_name='dummy',
                input_dir=input_dir,
                output_dir=os.path.join(output_dir, "factory_test"),
                chunk_size=(16, 32, 32)
            )
            print("✓ Factory pattern test passed")
        except Exception as e:
            print(f"✗ Factory pattern test failed: {e}")
            return False
        
        # Test 3: Check output files
        print("\n4. Checking output files...")
        output_files = []
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith('.zst'):
                    output_files.append(os.path.join(root, file))
        
        if len(output_files) >= 6:  # 3 files × 2 tests
            print(f"✓ Output files test passed ({len(output_files)} files created)")
        else:
            print(f"✗ Output files test failed (expected ≥6, got {len(output_files)})")
            return False
        
        # Test 4: Verify output data
        print("\n5. Verifying output data...")
        try:
            test_output = output_files[0]
            volume = Volume(test_output)
            volume.load()
            
            if volume.volume is not None and volume.volume.shape == (32, 64, 64):
                print("✓ Output data verification passed")
            else:
                print(f"✗ Output data verification failed (shape: {volume.volume.shape if volume.volume is not None else None})")
                return False
        except Exception as e:
            print(f"✗ Output data verification failed: {e}")
            return False
        
        # Test 5: List processors
        print("\n6. Testing processor listing...")
        processors = ProcessorFactory.list_processors()
        if 'dummy' in processors:
            print(f"✓ Processor listing test passed (found: {processors})")
        else:
            print(f"✗ Processor listing test failed (processors: {processors})")
            return False
    
    print("\n🎉 All tests passed! Framework is working correctly.")
    return True


def test_chunking():
    """
    Test chunking functionality specifically.
    """
    print("\nTesting chunking functionality...")
    
    from base_processor import BaseProcessor
    
    class ChunkTestProcessor(BaseProcessor):
        def setup_method(self):
            pass
        
        def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
            # Return chunk shape as a constant value for testing
            return np.full(chunk.shape, chunk.shape[0], dtype=np.uint16)
    
    # Test with different chunk sizes
    test_volume = np.random.randint(0, 256, (40, 80, 80), dtype=np.uint8)
    
    processor = ChunkTestProcessor(
        input_dir="dummy",
        output_dir="dummy",
        chunk_size=(20, 40, 40),
        overlap=(5, 10, 10)
    )
    
    chunks = processor.get_chunks(test_volume)
    print(f"Created {len(chunks)} chunks from volume shape {test_volume.shape}")
    
    # Process chunks
    for i, chunk in enumerate(chunks):
        chunk['result'] = processor.process_chunk(chunk['data'])
        print(f"Chunk {i}: position={chunk['position']}, size={chunk['size']}")
    
    # Merge chunks
    merged = processor.merge_chunks(chunks, test_volume.shape)
    print(f"Merged volume shape: {merged.shape}")
    
    if merged.shape == test_volume.shape:
        print("✓ Chunking test passed")
        return True
    else:
        print("✗ Chunking test failed")
        return False


if __name__ == "__main__":
    print("Universal Baseline Framework Test Suite")
    print("=" * 50)
    
    # Run tests
    success = True
    
    try:
        success &= test_framework()
        success &= test_chunking()
    except Exception as e:
        print(f"Test suite failed with error: {e}")
        success = False
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("\nYou can now use the framework with:")
        print("- python processor_factory.py processor=cellpose")
        print("- python processor_factory.py processor=micro_sam")
        print("- Or import and use the Python API")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
