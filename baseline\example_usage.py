"""
Example usage of the universal baseline framework.
"""

import os
from processor_factory import ProcessorFactory, run_baseline


def example_cellpose():
    """
    Example of running Cellpose with custom parameters.
    """
    print("Running Cellpose example...")
    
    run_baseline(
        processor_name='cellpose',
        input_dir='datasets/em_s0/val',
        output_dir='output/baseline/cellpose_custom',
        chunk_size=(256, 512, 512),
        overlap=(32, 64, 64),
        target_resolution=None,  # Keep original resolution
        # Cellpose-specific parameters
        model_type='cyto3',
        batch_size=16,
        do_3D=True,
        flow3D_smooth=1,
        niter=1000,
        diameter=None
    )


def example_micro_sam():
    """
    Example of running MicroSAM with custom parameters.
    """
    print("Running MicroSAM example...")
    
    run_baseline(
        processor_name='micro_sam',
        input_dir='datasets/em_s0/val',
        output_dir='output/baseline/micro_sam_custom',
        chunk_size=(128, 1024, 1024),
        overlap=(16, 128, 128),
        target_resolution=(512, 512, 512),  # Resize to 512^3
        # MicroSAM-specific parameters
        model_name='vit_b_em_organelles'
    )


def example_factory_usage():
    """
    Example of using the factory directly.
    """
    print("Factory usage example...")
    
    # List available processors
    print("Available processors:", ProcessorFactory.list_processors())
    
    # Create a processor instance
    processor = ProcessorFactory.create_processor(
        processor_name='cellpose',
        input_dir='datasets/em_s0/val',
        output_dir='output/baseline/cellpose_factory',
        chunk_size=(128, 256, 256),
        model_type='cyto2'
    )
    
    # Process all volumes
    processor.process_all()


def example_custom_processor():
    """
    Example of creating a custom processor.
    """
    from base_processor import BaseProcessor
    import numpy as np
    
    class DummyProcessor(BaseProcessor):
        """
        A dummy processor that just returns zeros.
        """
        
        def setup_method(self):
            print("Setting up dummy processor...")
        
        def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
            # Just return zeros with same shape
            return np.zeros(chunk.shape, dtype=np.uint16)
    
    # Register the custom processor
    ProcessorFactory.register_processor('dummy', DummyProcessor)
    
    # Use it
    run_baseline(
        processor_name='dummy',
        input_dir='datasets/em_s0/val',
        output_dir='output/baseline/dummy',
        chunk_size=(64, 128, 128)
    )


if __name__ == "__main__":
    # Run examples
    print("Running baseline framework examples...")
    
    # Uncomment the examples you want to run
    # example_cellpose()
    # example_micro_sam()
    # example_factory_usage()
    # example_custom_processor()
    
    print("Examples completed!")
