# Cellpose baseline configuration
processor: cellpose

# Input/Output paths (relative to datasets_root and output_root)
input_dir: em_s0/val
output_dir: baseline/cellpose

# Processing parameters
chunk_size: [256, 512, 512]
overlap: [32, 64, 64]
target_resolution: null  # Keep original resolution
file_extension: .zst

# Cellpose-specific parameters
model_type: cyto3
batch_size: 32
do_3D: true
flow3D_smooth: 1
niter: 1000
diameter: null  # Automatic diameter detection
