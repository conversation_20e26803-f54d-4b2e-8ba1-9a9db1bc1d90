"""
Universal baseline processor for 3D segmentation methods.
Provides a unified interface for data loading, chunking, format conversion, 
result merging and saving.
"""

import os
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Union, Any
from tqdm import tqdm
import hydra
from omegaconf import DictConfig

from dataprocess.volume import Volume


class BaseProcessor(ABC):
    """
    Abstract base class for baseline segmentation methods.
    
    This class provides a unified framework for:
    - Loading data from specified directories
    - Chunking large volumes for memory-efficient processing
    - Format conversion for different baseline methods
    - Merging results and saving to output directories
    """
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 chunk_size: Optional[Tuple[int, int, int]] = None,
                 overlap: Optional[Tuple[int, int, int]] = None,
                 target_resolution: Optional[Tuple[int, int, int]] = None,
                 file_extension: str = ".zst"):
        """
        Initialize the base processor.
        
        Args:
            input_dir: Directory containing input volumes
            output_dir: Directory to save results
            chunk_size: Size of chunks for processing (z, y, x). If None, process whole volume
            overlap: Overlap between chunks (z, y, x). Default is (0, 0, 0)
            target_resolution: Target resolution for resizing. If None, keep original
            file_extension: File extension to look for (.zst, .npz)
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.chunk_size = chunk_size
        self.overlap = overlap if overlap is not None else (0, 0, 0)
        self.target_resolution = target_resolution
        self.file_extension = file_extension
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize method-specific components
        self.setup_method()
    
    @abstractmethod
    def setup_method(self):
        """
        Initialize method-specific models, parameters, etc.
        This method should be implemented by each baseline method.
        """
        pass
    
    @abstractmethod
    def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
        """
        Process a single chunk of data.
        
        Args:
            chunk: Input volume chunk as numpy array
            
        Returns:
            Processed chunk as numpy array
        """
        pass
    
    def preprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Preprocess the volume before chunking.
        Can be overridden by subclasses for method-specific preprocessing.
        
        Args:
            volume: Input volume
            
        Returns:
            Preprocessed volume
        """
        return volume
    
    def postprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Postprocess the volume after merging chunks.
        Can be overridden by subclasses for method-specific postprocessing.
        
        Args:
            volume: Processed volume
            
        Returns:
            Postprocessed volume
        """
        return volume
    
    def get_chunks(self, volume: np.ndarray) -> List[Dict]:
        """
        Split volume into chunks for processing.
        
        Args:
            volume: Input volume
            
        Returns:
            List of chunk information dictionaries
        """
        if self.chunk_size is None:
            # Process whole volume
            return [{
                'data': volume,
                'position': (0, 0, 0),
                'size': volume.shape
            }]
        
        chunks = []
        z_size, y_size, x_size = volume.shape
        chunk_z, chunk_y, chunk_x = self.chunk_size
        overlap_z, overlap_y, overlap_x = self.overlap
        
        # Calculate step sizes
        step_z = max(1, chunk_z - overlap_z)
        step_y = max(1, chunk_y - overlap_y)
        step_x = max(1, chunk_x - overlap_x)
        
        for z in range(0, z_size, step_z):
            for y in range(0, y_size, step_y):
                for x in range(0, x_size, step_x):
                    # Calculate actual chunk boundaries
                    z_end = min(z + chunk_z, z_size)
                    y_end = min(y + chunk_y, y_size)
                    x_end = min(x + chunk_x, x_size)
                    
                    chunk_data = volume[z:z_end, y:y_end, x:x_end]
                    
                    chunks.append({
                        'data': chunk_data,
                        'position': (z, y, x),
                        'size': (z_end - z, y_end - y, x_end - x),
                        'global_position': (z, y, x),
                        'global_end': (z_end, y_end, x_end)
                    })
        
        return chunks
    
    def merge_chunks(self, chunks: List[Dict], original_shape: Tuple[int, int, int]) -> np.ndarray:
        """
        Merge processed chunks back into a single volume.
        
        Args:
            chunks: List of processed chunk dictionaries
            original_shape: Shape of the original volume
            
        Returns:
            Merged volume
        """
        if len(chunks) == 1:
            return chunks[0]['result']
        
        # Initialize output volume
        first_result = chunks[0]['result']
        if first_result.dtype == np.bool_:
            merged_volume = np.zeros(original_shape, dtype=np.uint8)
        else:
            merged_volume = np.zeros(original_shape, dtype=first_result.dtype)
        
        # Handle overlapping regions by averaging or taking maximum
        overlap_count = np.zeros(original_shape, dtype=np.int32)
        
        for chunk in chunks:
            result = chunk['result']
            pos = chunk['global_position']
            end = chunk['global_end']
            
            z_start, y_start, x_start = pos
            z_end, y_end, x_end = end
            
            # Add to merged volume
            if result.dtype == np.bool_:
                merged_volume[z_start:z_end, y_start:y_end, x_start:x_end] += result.astype(np.uint8)
            else:
                merged_volume[z_start:z_end, y_start:y_end, x_start:x_end] += result
            
            overlap_count[z_start:z_end, y_start:y_end, x_start:x_end] += 1
        
        # Average overlapping regions
        overlap_mask = overlap_count > 1
        merged_volume[overlap_mask] = merged_volume[overlap_mask] / overlap_count[overlap_mask]
        
        # Convert back to appropriate type
        if first_result.dtype == np.bool_:
            merged_volume = (merged_volume > 0.5).astype(np.uint8)
        
        return merged_volume
    
    def process_volume(self, volume_path: str) -> np.ndarray:
        """
        Process a single volume file.
        
        Args:
            volume_path: Path to the volume file
            
        Returns:
            Processed volume
        """
        # Load volume
        volume = Volume(volume_path)
        volume.load()
        original_shape = volume.volume.shape
        
        # Resize if target resolution is specified
        if self.target_resolution is not None:
            volume.scale_volume_to(self.target_resolution)
        
        # Preprocess
        processed_volume = self.preprocess_volume(volume.volume)
        
        # Get chunks
        chunks = self.get_chunks(processed_volume)
        
        # Process each chunk
        for chunk in tqdm(chunks, desc="Processing chunks", leave=False):
            chunk['result'] = self.process_chunk(chunk['data'])
        
        # Merge chunks
        if self.target_resolution is not None:
            merged_volume = self.merge_chunks(chunks, self.target_resolution)
            # Scale back to original resolution
            result_volume = Volume(None)
            result_volume.volume = merged_volume
            result_volume.scale_volume_to(original_shape)
            merged_volume = result_volume.volume
        else:
            merged_volume = self.merge_chunks(chunks, original_shape)
        
        # Postprocess
        final_volume = self.postprocess_volume(merged_volume)
        
        return final_volume
    
    def process_all(self):
        """
        Process all volumes in the input directory.
        """
        # Get all volume files
        files = [f for f in os.listdir(self.input_dir) if f.endswith(self.file_extension)]
        
        for file in tqdm(files, desc="Processing volumes"):
            input_path = os.path.join(self.input_dir, file)
            output_path = os.path.join(self.output_dir, file)
            
            try:
                # Process volume
                result_volume = self.process_volume(input_path)
                
                # Save result
                output_vol = Volume(None)
                output_vol.volume = result_volume
                output_vol.save_volume(output_path)
                
                print(f"Processed: {file}")
                
            except Exception as e:
                print(f"Error processing {file}: {str(e)}")
                continue


def create_hydra_main(processor_class):
    """
    Create a hydra main function for a processor class.
    
    Args:
        processor_class: The processor class to use
        
    Returns:
        Decorated main function
    """
    @hydra.main(config_path="../config", config_name="config", version_base=None)
    def main(cfg: DictConfig):
        # Default paths
        input_dir = os.path.join(cfg.datasets_root, "em_s0/val")
        output_dir = os.path.join(cfg.output_root, f"baseline/{processor_class.__name__.lower()}")
        
        # Create and run processor
        processor = processor_class(input_dir=input_dir, output_dir=output_dir)
        processor.process_all()
    
    return main
