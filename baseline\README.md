# Universal Baseline Framework for 3D Segmentation

This framework provides a unified interface for running various baseline segmentation methods on 3D volumes. It handles data loading, chunking for memory efficiency, format conversion, result merging, and saving.

## Features

- **Unified Interface**: Same API for all baseline methods
- **Memory Efficient**: Automatic chunking for large volumes
- **Flexible**: Support for different resolutions and formats
- **Extensible**: Easy to add new baseline methods
- **Simple Configuration**: Minimal code required for new baselines

## Quick Start

### 1. Using Command Line with Hydra

```bash
# Run Cellpose
python processor_factory.py processor=cellpose

# Run MicroSAM with custom parameters
python processor_factory.py processor=micro_sam chunk_size=[128,512,512] overlap=[16,64,64]

# Run with custom input/output directories
python processor_factory.py processor=cellpose input_dir=datasets/custom/val output_dir=output/custom/cellpose
```

### 2. Using Python API

```python
from baseline.processor_factory import run_baseline

# Simple usage
run_baseline(
    processor_name='cellpose',
    input_dir='datasets/em_s0/val',
    output_dir='output/baseline/cellpose'
)

# With custom parameters
run_baseline(
    processor_name='cellpose',
    input_dir='datasets/em_s0/val',
    output_dir='output/baseline/cellpose_custom',
    chunk_size=(256, 512, 512),
    overlap=(32, 64, 64),
    target_resolution=(512, 512, 512),
    # Method-specific parameters
    model_type='cyto3',
    batch_size=16,
    do_3D=True
)
```

### 3. Using Individual Processors

```python
from baseline.cellpose_processor import CellposeProcessor

processor = CellposeProcessor(
    input_dir='datasets/em_s0/val',
    output_dir='output/baseline/cellpose',
    chunk_size=(256, 512, 512),
    model_type='cyto3'
)
processor.process_all()
```

## Available Processors

### Cellpose
- **Name**: `cellpose`
- **Parameters**:
  - `model_type`: Model type (default: 'cyto3')
  - `batch_size`: Batch size (default: 32)
  - `do_3D`: Use 3D processing (default: True)
  - `flow3D_smooth`: 3D flow smoothing (default: 1)
  - `niter`: Number of iterations (default: 1000)
  - `diameter`: Cell diameter (default: None for automatic)

### MicroSAM
- **Name**: `micro_sam` or `microsam`
- **Parameters**:
  - `model_name`: Model name (default: 'vit_b_em_organelles')

## Configuration Parameters

### Common Parameters
- `input_dir`: Directory containing input volumes
- `output_dir`: Directory to save results
- `chunk_size`: Size of chunks for processing (z, y, x). If None, process whole volume
- `overlap`: Overlap between chunks (z, y, x). Default: (0, 0, 0)
- `target_resolution`: Target resolution for resizing. If None, keep original
- `file_extension`: File extension to look for (default: '.zst')

### Default Chunk Sizes
- **Cellpose**: (256, 512, 512) with overlap (32, 64, 64)
- **MicroSAM**: (128, 1024, 1024) with overlap (16, 128, 128)

## Adding New Baseline Methods

### 1. Create a New Processor Class

```python
from baseline.base_processor import BaseProcessor
import numpy as np

class YourMethodProcessor(BaseProcessor):
    def __init__(self, input_dir, output_dir, **kwargs):
        # Add method-specific parameters
        self.your_param = kwargs.get('your_param', 'default_value')
        super().__init__(input_dir, output_dir, **kwargs)
    
    def setup_method(self):
        # Initialize your method (load models, etc.)
        self.model = YourMethod()
    
    def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
        # Process a single chunk
        result = self.model.segment(chunk)
        return result
    
    def preprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        # Optional: preprocess before chunking
        return volume
    
    def postprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        # Optional: postprocess after merging
        return volume
```

### 2. Register the Processor

```python
from baseline.processor_factory import ProcessorFactory

ProcessorFactory.register_processor('your_method', YourMethodProcessor)
```

### 3. Use It

```python
run_baseline(
    processor_name='your_method',
    input_dir='datasets/em_s0/val',
    output_dir='output/baseline/your_method',
    your_param='custom_value'
)
```

## Memory Management

The framework automatically handles memory management through chunking:

1. **Automatic Chunking**: Large volumes are split into smaller chunks
2. **Overlap Handling**: Overlapping regions are averaged during merging
3. **Resolution Scaling**: Volumes can be scaled down for processing and scaled back up
4. **Efficient Merging**: Results are merged back into full-resolution volumes

## File Format Support

- **Input**: `.zst` (compressed), `.npz` (numpy)
- **Output**: Same format as input
- **Volume Class**: Uses the existing `Volume` class for I/O operations

## Error Handling

- Individual file processing errors don't stop the entire batch
- Failed chunks return zero masks to maintain volume structure
- Detailed error messages for debugging

## Examples

See `example_usage.py` for detailed examples of:
- Basic usage with different methods
- Custom parameter configuration
- Factory pattern usage
- Creating custom processors

## Integration with Existing Code

This framework is designed to be compatible with your existing baseline scripts:

```python
# Old way (cellpose_val.py)
@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    # Manual implementation...

# New way
from baseline.cellpose_processor import main
# Uses the same hydra configuration automatically
```
