# Unified Evaluation System (evaluate.py)

## Overview

The new `evaluate.py` is a unified, efficient evaluation system that supports both semantic and instance segmentation evaluation. It replaces the old `evaluate.py` (now renamed to `evaluate_old.py`) with a cleaner, more efficient implementation.

## Key Features

- **Unified Interface**: Single script for both semantic and instance segmentation evaluation
- **Efficient Algorithms**: Uses optimized functions from `metrics.py`
- **Clean Code Structure**: Well-organized, maintainable code
- **Consistent Interface**: Maintains compatibility with existing configuration system
- **Multiple Metrics**: Comprehensive evaluation metrics for both modes

## Usage

### Command Line Usage

```bash
# Semantic segmentation evaluation (default)
python predict/evaluate.py

# Instance segmentation evaluation
python predict/evaluate.py instance_mode=true

# Custom thresholds for instance evaluation
python predict/evaluate.py instance_mode=true thresholds=[0.5,0.75,0.9]

# Custom size mismatch handling
python predict/evaluate.py size_mismatch_mode=crop
```

### Configuration Parameters

```yaml
# Mode selection
instance_mode: false  # true for instance segmentation, false for semantic

# Instance segmentation parameters
thresholds: [0.5, 0.75, 0.9]  # IoU thresholds for evaluation

# General parameters
size_mismatch_mode: "scale"    # "scale" or "crop"
use_specific_format: true      # Use mito_seg format
use_datasets_info: true        # Use dataset info structure

# Path parameters
pred_dir: "${output_root}/evaluate"
true_dir: "${datasets_root}/main/train/mitoem/seg/mito"
```

## Evaluation Modes

### Semantic Segmentation (instance_mode=false)

Evaluates binary segmentation using the following metrics:
- **Dice Coefficient**: Harmonic mean of precision and recall
- **IoU (Intersection over Union)**: Jaccard index
- **Precision**: TP / (TP + FP)
- **Recall**: TP / (TP + FN)
- **Accuracy**: TP / (TP + FP + FN)

Output file: `metrics_{split}.json`

### Instance Segmentation (instance_mode=true)

Evaluates instance segmentation using the `average_precision` function:
- **mAP (mean Average Precision)**: Average across multiple IoU thresholds
- **Precision**: Correctly matched instances / Total predicted instances
- **Recall**: Correctly matched instances / Total ground truth instances
- **Accuracy**: TP / (TP + FP + FN)
- **Instance Counts**: Predicted, ground truth, and matched instances

Output file: `instance_metrics_{split}.json`

## Functions

### Core Functions

- `compute_semantic_metrics(preds, labels)`: Compute semantic segmentation metrics
- `compute_instance_metrics_ap(pred_volume, gt_volume, thresholds)`: Compute instance metrics using average_precision
- `evaluate_volume_semantic(pred_path, true_path, ...)`: Evaluate semantic segmentation for a file pair
- `evaluate_volume_inst(pred_path, true_path, ...)`: Evaluate instance segmentation for a file pair

### Utility Functions

- `preprocess_volume_size_mismatch(...)`: Handle volume size mismatches
- `get_evaluation_files(...)`: Get file pairs for evaluation
- `get_files_from_datasets(...)`: Get files from dataset configuration

## Output Format

### Semantic Segmentation Output

```json
{
  "file_id_or_path": {
    "dice": 0.95,
    "iou": 0.90,
    "precision": 0.92,
    "recall": 0.98,
    "accuracy": 0.90,
    "processing_time": 1.2
  },
  "average": {
    "dice": 0.93,
    "iou": 0.88,
    "precision": 0.90,
    "recall": 0.96,
    "accuracy": 0.88
  }
}
```

### Instance Segmentation Output

```json
{
  "file_id_or_path": {
    "mAP": 0.85,
    "precision": 0.90,
    "recall": 0.80,
    "accuracy": 0.75,
    "matched_instances": 45,
    "total_pred_instances": 50,
    "total_gt_instances": 56,
    "AP_values": {
      "AP50": 0.90,
      "AP75": 0.85,
      "AP90": 0.80
    },
    "detailed_metrics": {
      "AP50": {
        "ap": 0.90,
        "precision": 0.90,
        "recall": 0.85,
        "accuracy": 0.78,
        "tp": 45,
        "fp": 5,
        "fn": 11
      }
    },
    "processing_time": 2.5
  },
  "average": {
    "mAP": 0.82,
    "precision": 0.88,
    "recall": 0.78,
    "accuracy": 0.73
  }
}
```

## Migration from Old System

### Changes Made

1. **File Renaming**:
   - Old `evaluate.py` → `evaluate_old.py`
   - New `evaluate_inst.py` → `evaluate.py`

2. **Metrics Functions**:
   - Added `compute_semantic_metrics()` to `metrics.py`
   - Unified evaluation interface

3. **Configuration**:
   - Added `instance_mode` parameter
   - Maintained backward compatibility

### Backward Compatibility

The new system maintains full backward compatibility with existing configurations. Simply set `instance_mode=false` for semantic segmentation evaluation (which is the default).

## Performance

The new system offers improved performance through:
- Optimized algorithms from `metrics.py`
- Cleaner code structure
- Reduced memory overhead
- Efficient file I/O handling

## Testing

The system has been thoroughly tested with:
- Perfect match scenarios
- No overlap scenarios
- Mixed overlap scenarios
- File I/O operations
- Both semantic and instance modes
