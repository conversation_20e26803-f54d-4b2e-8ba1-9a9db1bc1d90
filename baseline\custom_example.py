"""
Example of creating a custom baseline processor.

This demonstrates how to implement a new baseline method using the framework.
"""

import numpy as np
from typing import Tuple, Optional
from scipy import ndimage
from skimage.segmentation import watershed
from skimage.feature import peak_local_maxima

from base_processor import BaseProcessor, create_hydra_main
from processor_factory import ProcessorFactory


class WatershedProcessor(BaseProcessor):
    """
    Example custom processor using watershed segmentation.
    
    This is a simple example that demonstrates:
    - Custom preprocessing
    - Method-specific parameters
    - Custom postprocessing
    """
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 chunk_size: Optional[Tuple[int, int, int]] = None,
                 overlap: Optional[Tuple[int, int, int]] = None,
                 target_resolution: Optional[Tuple[int, int, int]] = None,
                 file_extension: str = ".zst",
                 # Watershed-specific parameters
                 sigma: float = 1.0,
                 min_distance: int = 10,
                 threshold_abs: float = 0.3,
                 exclude_border: bool = True):
        """
        Initialize Watershed processor.
        
        Args:
            input_dir: Directory containing input volumes
            output_dir: Directory to save results
            chunk_size: Size of chunks for processing. Default: (128, 256, 256)
            overlap: Overlap between chunks. Default: (16, 32, 32)
            target_resolution: Target resolution for resizing. If None, keep original
            file_extension: File extension to look for
            sigma: Gaussian smoothing sigma
            min_distance: Minimum distance between peaks
            threshold_abs: Absolute threshold for peak detection
            exclude_border: Whether to exclude border peaks
        """
        # Set default chunk size and overlap for watershed
        if chunk_size is None:
            chunk_size = (128, 256, 256)
        if overlap is None:
            overlap = (16, 32, 32)
            
        self.sigma = sigma
        self.min_distance = min_distance
        self.threshold_abs = threshold_abs
        self.exclude_border = exclude_border
        
        super().__init__(input_dir, output_dir, chunk_size, overlap, target_resolution, file_extension)
    
    def setup_method(self):
        """
        Initialize watershed method.
        """
        print(f"Setting up Watershed processor with sigma={self.sigma}, min_distance={self.min_distance}")
    
    def preprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Preprocess volume for watershed.
        
        Args:
            volume: Input volume
            
        Returns:
            Preprocessed volume
        """
        # Normalize to 0-1 range
        volume = volume.astype(np.float32)
        volume = (volume - volume.min()) / (volume.max() - volume.min())
        
        # Apply Gaussian smoothing
        if self.sigma > 0:
            volume = ndimage.gaussian_filter(volume, sigma=self.sigma)
        
        return volume
    
    def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
        """
        Process a single chunk with watershed segmentation.
        
        Args:
            chunk: Input volume chunk
            
        Returns:
            Segmentation mask
        """
        try:
            # Find local maxima as seeds
            # For 3D, we'll process slice by slice for simplicity
            labels = np.zeros(chunk.shape, dtype=np.uint16)
            label_counter = 1
            
            for z in range(chunk.shape[0]):
                slice_2d = chunk[z]
                
                # Find local maxima
                peaks = peak_local_maxima(
                    slice_2d,
                    min_distance=self.min_distance,
                    threshold_abs=self.threshold_abs,
                    exclude_border=self.exclude_border
                )
                
                if len(peaks[0]) == 0:
                    continue
                
                # Create markers
                markers = np.zeros(slice_2d.shape, dtype=np.int32)
                for i, (y, x) in enumerate(zip(peaks[0], peaks[1])):
                    markers[y, x] = label_counter + i
                
                # Apply watershed
                slice_labels = watershed(-slice_2d, markers, mask=slice_2d > 0.1)
                
                # Add to 3D labels
                labels[z] = slice_labels.astype(np.uint16)
                label_counter += len(peaks[0])
            
            return labels
            
        except Exception as e:
            print(f"Error processing chunk: {str(e)}")
            # Return empty mask with same shape
            return np.zeros(chunk.shape, dtype=np.uint16)
    
    def postprocess_volume(self, volume: np.ndarray) -> np.ndarray:
        """
        Postprocess the merged volume.
        
        Args:
            volume: Merged volume
            
        Returns:
            Final processed volume
        """
        # Remove small objects
        unique_labels = np.unique(volume)
        for label in unique_labels:
            if label == 0:
                continue
            
            mask = volume == label
            if np.sum(mask) < 100:  # Remove objects smaller than 100 voxels
                volume[mask] = 0
        
        # Relabel to ensure consecutive labels
        unique_labels = np.unique(volume)
        if len(unique_labels) > 1:
            for i, label in enumerate(unique_labels[1:], 1):  # Skip 0
                volume[volume == label] = i
        
        return volume.astype(np.uint16)


class ThresholdProcessor(BaseProcessor):
    """
    Simple threshold-based processor for demonstration.
    """
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 threshold: float = 0.5,
                 **kwargs):
        self.threshold = threshold
        super().__init__(input_dir, output_dir, **kwargs)
    
    def setup_method(self):
        print(f"Setting up Threshold processor with threshold={self.threshold}")
    
    def process_chunk(self, chunk: np.ndarray) -> np.ndarray:
        # Simple thresholding
        normalized = (chunk - chunk.min()) / (chunk.max() - chunk.min())
        binary = (normalized > self.threshold).astype(np.uint8)
        
        # Label connected components
        labeled, num_labels = ndimage.label(binary)
        return labeled.astype(np.uint16)


def register_custom_processors():
    """
    Register custom processors with the factory.
    """
    ProcessorFactory.register_processor('watershed', WatershedProcessor)
    ProcessorFactory.register_processor('threshold', ThresholdProcessor)
    print("Registered custom processors: watershed, threshold")


def example_usage():
    """
    Example of using custom processors.
    """
    from processor_factory import run_baseline
    
    # Register custom processors
    register_custom_processors()
    
    # Use watershed processor
    run_baseline(
        processor_name='watershed',
        input_dir='datasets/em_s0/val',
        output_dir='output/baseline/watershed',
        chunk_size=(64, 128, 128),
        sigma=1.5,
        min_distance=15,
        threshold_abs=0.2
    )
    
    # Use threshold processor
    run_baseline(
        processor_name='threshold',
        input_dir='datasets/em_s0/val',
        output_dir='output/baseline/threshold',
        threshold=0.3
    )


# Create main functions for individual processors
watershed_main = create_hydra_main(WatershedProcessor)
threshold_main = create_hydra_main(ThresholdProcessor)


if __name__ == "__main__":
    # Register processors
    register_custom_processors()
    
    # Run example
    print("Available processors:", ProcessorFactory.list_processors())
    
    # Uncomment to run examples
    # example_usage()
