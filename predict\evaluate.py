import os
import numpy as np
import json
import hydra
import time
import logging
from dataprocess.volume import Volume
from tqdm import tqdm
from utils.misc import convert_to_json_serializable
from predict.metrics import average_precision, compute_semantic_metrics

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def compute_instance_metrics_ap(pred_volume, gt_volume, thresholds=[0.5, 0.75, 0.9]):
    """
    Compute instance segmentation metrics using average_precision function

    Args:
        pred_volume: Predicted instance segmentation volume
        gt_volume: Ground truth instance segmentation volume
        thresholds: List of IoU thresholds for evaluation

    Returns:
        dict: Dictionary containing computed metrics
    """
    # Use average_precision function to compute AP, TP, FP, FN
    ap, tp, fp, fn = average_precision(gt_volume, pred_volume, thresholds)

    # Get instance counts
    pred_instance_ids = np.unique(pred_volume)
    pred_instance_ids = pred_instance_ids[pred_instance_ids > 0]
    gt_instance_ids = np.unique(gt_volume)
    gt_instance_ids = gt_instance_ids[gt_instance_ids > 0]

    n_pred = len(pred_instance_ids)
    n_gt = len(gt_instance_ids)

    # Compute metrics for each threshold
    metrics = {}
    for i, threshold in enumerate(thresholds):
        tp_val = float(tp[i])
        fp_val = float(fp[i])
        fn_val = float(fn[i])
        ap_val = float(ap[i])

        # Compute precision, recall, accuracy
        precision = tp_val / (tp_val + fp_val) if (tp_val + fp_val) > 0 else 0.0
        recall = tp_val / (tp_val + fn_val) if (tp_val + fn_val) > 0 else 0.0
        accuracy = (
            tp_val / (tp_val + fp_val + fn_val)
            if (tp_val + fp_val + fn_val) > 0
            else 0.0
        )

        metrics[f"AP{int(threshold*100)}"] = {
            "ap": ap_val,
            "precision": precision,
            "recall": recall,
            "accuracy": accuracy,
            "tp": tp_val,
            "fp": fp_val,
            "fn": fn_val,
        }

    # Compute mean AP
    mAP = float(np.mean(ap))

    # Compute overall metrics using the first threshold (typically 0.5)
    main_threshold_idx = 0
    main_tp = float(tp[main_threshold_idx])
    main_fp = float(fp[main_threshold_idx])
    main_fn = float(fn[main_threshold_idx])

    overall_precision = (
        main_tp / (main_tp + main_fp) if (main_tp + main_fp) > 0 else 0.0
    )
    overall_recall = main_tp / (main_tp + main_fn) if (main_tp + main_fn) > 0 else 0.0
    overall_accuracy = (
        main_tp / (main_tp + main_fp + main_fn)
        if (main_tp + main_fp + main_fn) > 0
        else 0.0
    )

    return {
        "mAP": mAP,
        "precision": overall_precision,
        "recall": overall_recall,
        "accuracy": overall_accuracy,
        "matched_instances": int(main_tp),
        "total_pred_instances": n_pred,
        "total_gt_instances": n_gt,
        "AP_values": {
            f"AP{int(t*100)}": float(ap[i]) for i, t in enumerate(thresholds)
        },
        "detailed_metrics": metrics,
    }


def evaluate_volume_semantic(
    pred_path,
    true_path,
    size_mismatch_mode="scale",
    pred_ori_size=None,
):
    """
    Evaluate semantic segmentation results for a single volume

    Args:
        pred_path: Path to prediction file
        true_path: Path to ground truth file
        size_mismatch_mode: Mode for handling size mismatch ("scale" or "crop")
        pred_ori_size: Original prediction size for scaling

    Returns:
        dict: Evaluation metrics
    """
    start_time = time.time()

    # Load volumes
    pred_mask = Volume(pred_path)
    pred_mask.load()
    true_mask = Volume(true_path)
    true_mask.load()

    # Convert to binary if not already
    true_mask.volume_to_binary_8bit()

    # Handle size mismatch
    preprocess_volume_size_mismatch(
        pred_mask, true_mask, size_mismatch_mode, pred_ori_size
    )

    # Compute semantic metrics
    result = compute_semantic_metrics(pred_mask.volume, true_mask.volume)

    # Add processing time
    processing_time = time.time() - start_time
    result["processing_time"] = processing_time

    return result


def preprocess_volume_size_mismatch(
    pred_mask, true_mask, size_mismatch_mode="scale", pred_ori_size=None
):
    """
    Preprocess volume size mismatch between prediction and ground truth

    Args:
        pred_mask: Prediction volume (Volume object)
        true_mask: Ground truth volume (Volume object)
        size_mismatch_mode: Processing mode, "scale" or "crop"
        pred_ori_size: Original prediction size for scaling
    """
    if pred_mask.volume.shape == true_mask.volume.shape:
        return

    pred_shape = pred_mask.volume.shape
    true_shape = true_mask.volume.shape

    logger.info(f"Volume size mismatch detected:")
    logger.info(f"  Prediction shape: {pred_shape}")
    logger.info(f"  Ground truth shape: {true_shape}")
    logger.info(f"  Processing mode: {size_mismatch_mode}")

    if pred_ori_size is not None:
        pred_mask.scale_volume_to(pred_ori_size)

    if size_mismatch_mode == "scale":
        logger.info("Scaling prediction volume to match ground truth size...")
        pred_mask.scale_volume_to(true_shape)
        logger.info(f"  Scaled prediction shape: {pred_mask.volume.shape}")

    elif size_mismatch_mode == "crop":
        logger.info("Cropping prediction volume to match ground truth size...")

        crop_z = min(pred_shape[0], true_shape[0])
        crop_y = min(pred_shape[1], true_shape[1])
        crop_x = min(pred_shape[2], true_shape[2])

        pred_mask.volume = pred_mask.volume[:crop_z, :crop_y, :crop_x]

        if pred_mask.volume.shape != true_shape:
            final_volume = np.zeros(true_shape, dtype=pred_mask.volume.dtype)

            copy_z = min(pred_mask.volume.shape[0], true_shape[0])
            copy_y = min(pred_mask.volume.shape[1], true_shape[1])
            copy_x = min(pred_mask.volume.shape[2], true_shape[2])

            final_volume[:copy_z, :copy_y, :copy_x] = pred_mask.volume[
                :copy_z, :copy_y, :copy_x
            ]
            pred_mask.volume = final_volume

        logger.info(f"  Cropped prediction shape: {pred_mask.volume.shape}")

    else:
        raise ValueError(f"Unsupported size_mismatch_mode: {size_mismatch_mode}")


def evaluate_volume_inst(
    pred_path,
    true_path,
    thresholds=[0.5, 0.75, 0.9],
    size_mismatch_mode="scale",
    pred_ori_size=None,
):
    """
    Evaluate instance segmentation results for a single volume

    Args:
        pred_path: Path to prediction file
        true_path: Path to ground truth file
        thresholds: List of IoU thresholds for evaluation
        size_mismatch_mode: Mode for handling size mismatch ("scale" or "crop")
        pred_ori_size: Original prediction size for scaling

    Returns:
        dict: Evaluation metrics
    """
    start_time = time.time()

    # Load volumes
    pred_mask = Volume(pred_path)
    pred_mask.load()
    true_mask = Volume(true_path)
    true_mask.load()

    # Handle size mismatch
    preprocess_volume_size_mismatch(
        pred_mask, true_mask, size_mismatch_mode, pred_ori_size
    )

    # Compute instance metrics
    result = compute_instance_metrics_ap(pred_mask.volume, true_mask.volume, thresholds)

    # Add processing time
    processing_time = time.time() - start_time
    result["processing_time"] = processing_time

    return result


def get_evaluation_files(pred_dir, true_dir, use_specific_format=True):
    """
    Get list of files to evaluate

    Args:
        pred_dir: Prediction directory
        true_dir: Ground truth directory
        use_specific_format: Whether to use specific format (mito_seg format)

    Returns:
        list: List of file information dictionaries
    """
    files_to_evaluate = []

    if use_specific_format:
        for file in os.listdir(pred_dir):
            if file.endswith(".zst") or file.endswith(".npz"):
                idx_with_surfix = file.split("_")[-2:]
                idx = idx_with_surfix[0]
                surfix = idx_with_surfix[1].split(".")[0]

                true_path = os.path.join(true_dir, f"mito_seg_{idx}.zst")
                if not os.path.exists(true_path):
                    true_path = os.path.join(true_dir, f"mito_seg_{idx}.npz")

                if not os.path.exists(true_path):
                    logger.warning(f"Ground truth not found for {file}")
                    continue

                files_to_evaluate.append(
                    {
                        "pred_path": os.path.join(pred_dir, file),
                        "true_path": true_path,
                        "idx": idx,
                        "surfix": surfix,
                    }
                )
    else:
        pred_files = sorted(
            [f for f in os.listdir(pred_dir) if f.endswith((".zst", ".npz"))]
        )
        true_files = sorted(
            [f for f in os.listdir(true_dir) if f.endswith((".zst", ".npz"))]
        )

        if len(pred_files) != len(true_files):
            logger.warning(
                f"Number of prediction files ({len(pred_files)}) does not match ground truth files ({len(true_files)})"
            )
            min_len = min(len(pred_files), len(true_files))
            pred_files = pred_files[:min_len]
            true_files = true_files[:min_len]

        for pred_file, true_file in zip(pred_files, true_files):
            pred_path = os.path.join(pred_dir, pred_file)
            true_path = os.path.join(true_dir, true_file)

            files_to_evaluate.append(
                {
                    "pred_path": pred_path,
                    "true_path": true_path,
                    "idx": None,
                    "surfix": None,
                }
            )

    return files_to_evaluate


def get_files_from_datasets(
    root_dir, datasets_info, split="train", true_dir_name=None, pred_dir_name=None
):
    """
    Process dataset information in the format used by get_datasets function

    Args:
        root_dir: Root directory containing the dataset
        datasets_info: List of dataset information dictionaries
        split: Dataset split (train, val, test)
        true_dir_name: Name of true directory
        pred_dir_name: Name of prediction directory

    Returns:
        list: List of dataset dictionaries
    """
    pred_dir_name = pred_dir_name or "sdfu"
    true_dir_name = true_dir_name or "sdf"
    datasets = []

    for dataset_info in datasets_info:
        name = dataset_info["name"]
        organelles = dataset_info["organelles"]
        for organelle in organelles:
            pred_dir = os.path.join(
                root_dir, split, name, pred_dir_name, organelle["seg"]
            )
            true_dir = os.path.join(
                root_dir, split, name, true_dir_name, organelle["seg"]
            )

            if not os.path.exists(pred_dir):
                logger.warning(f"Volume directory not found: {pred_dir}")
                continue
            if not os.path.exists(true_dir):
                logger.warning(f"Masks directory not found: {true_dir}")
                continue

            pred_files = sorted(
                [
                    os.path.join(pred_dir, f)
                    for f in os.listdir(pred_dir)
                    if f.endswith(".zst")
                ]
            )
            true_files = sorted(
                [
                    os.path.join(true_dir, f)
                    for f in os.listdir(true_dir)
                    if f.endswith(".zst")
                ]
            )

            if len(pred_files) != len(true_files):
                logger.warning(f"Number of volume and masks files should be the same")
                continue

            for volume_file, masks_file in zip(pred_files, true_files):
                datasets.append(
                    {
                        "pred_path": volume_file,
                        "true_path": masks_file,
                        "idx": None,
                        "surfix": None,
                    }
                )

    return datasets


@hydra.main(config_path="../config/task", config_name="evaluate", version_base=None)
def main(cfg):
    """
    Main function for instance segmentation evaluation
    """
    # Default paths
    pred_dir = cfg.get("pred_dir", os.path.join(cfg.output_root, "evaluate"))
    true_dir = cfg.get(
        "true_dir", os.path.join(cfg.datasets_root, "main/train/mitoem/seg/mito")
    )

    # Evaluation parameters
    instance_mode = cfg.get("instance_mode", False)
    thresholds = cfg.get("thresholds", [0.5, 0.75, 0.9])
    use_specific_format = cfg.get("use_specific_format", True)
    use_datasets_info = cfg.get("use_datasets_info", True)
    size_mismatch_mode = cfg.get("size_mismatch_mode", "scale")
    pred_ori_size = cfg.get("pred_ori_size", None)

    # Get split information for output filename
    split = "unknown"
    if use_datasets_info and hasattr(cfg, "path_info") and cfg.path_info:
        split = cfg.path_info.get("split", "unknown")

    # Support custom output file suffix
    custom_suffix = cfg.get("custom_suffix", None)
    if custom_suffix:
        split = custom_suffix

    if instance_mode:
        logger.info(f"Starting instance segmentation evaluation")
        logger.info(f"IoU thresholds: {thresholds}")
    else:
        logger.info(f"Starting semantic segmentation evaluation")

    logger.info(f"Size mismatch mode: {size_mismatch_mode}")
    logger.info(f"Processing split: {split}")

    if use_datasets_info:
        logger.info(f"Using datasets_info mode")
    else:
        logger.info(f"Prediction directory: {pred_dir}")
        logger.info(f"Ground truth directory: {true_dir}")

    # Get files to evaluate
    if use_datasets_info:
        path_info = cfg.path_info
        split = path_info.split
        root_dir = path_info[split]["root_dir"]
        pred_dir_name = path_info["pred_dir_name"]
        true_dir_name = path_info["true_dir_name"]
        datasets_info = path_info[split]["datasets_info"]
        files_to_evaluate = get_files_from_datasets(
            root_dir,
            datasets_info,
            split=split,
            true_dir_name=true_dir_name,
            pred_dir_name=pred_dir_name,
        )
    else:
        files_to_evaluate = get_evaluation_files(
            pred_dir, true_dir, use_specific_format
        )

    logger.info(f"Found {len(files_to_evaluate)} files to evaluate")

    # Process all files
    metrics_all = {}
    for file_info in tqdm(files_to_evaluate, desc="Evaluating files"):
        pred_path = file_info["pred_path"]
        true_path = file_info["true_path"]
        idx = file_info["idx"]
        surfix = file_info["surfix"]

        file_name = os.path.basename(pred_path)
        logger.info(f"Evaluating {file_name}...")

        # Evaluate based on mode
        if instance_mode:
            metrics = evaluate_volume_inst(
                pred_path,
                true_path,
                thresholds=thresholds,
                size_mismatch_mode=size_mismatch_mode,
                pred_ori_size=pred_ori_size,
            )

            # Log instance segmentation results
            logger.info(f"  Predicted instances: {metrics['total_pred_instances']}")
            logger.info(f"  Ground truth instances: {metrics['total_gt_instances']}")
            logger.info(f"  Matched instances: {metrics['matched_instances']}")
            logger.info(f"  mAP: {metrics['mAP']:.4f}")
            logger.info(f"  Precision: {metrics['precision']:.4f}")
            logger.info(f"  Recall: {metrics['recall']:.4f}")
            logger.info(f"  Accuracy: {metrics['accuracy']:.4f}")
        else:
            metrics = evaluate_volume_semantic(
                pred_path,
                true_path,
                size_mismatch_mode=size_mismatch_mode,
                pred_ori_size=pred_ori_size,
            )

            # Log semantic segmentation results
            logger.info(f"  Dice: {metrics['dice']:.4f}")
            logger.info(f"  IoU: {metrics['iou']:.4f}")
            logger.info(f"  Precision: {metrics['precision']:.4f}")
            logger.info(f"  Recall: {metrics['recall']:.4f}")

        if "processing_time" in metrics:
            logger.info(f"  Processing time: {metrics['processing_time']:.2f} seconds")

        # Store results based on structure
        if idx is not None and surfix is not None:
            if idx not in metrics_all:
                metrics_all[idx] = {}
            metrics_all[idx][surfix] = metrics
        else:
            metrics_all[pred_path] = metrics

    # Calculate average metrics
    avg_metrics = {}

    # Check data structure type
    if use_specific_format and not use_datasets_info:
        # Nested structure: metrics_all[idx][surfix]
        for idx, metrics_by_suffix in metrics_all.items():
            if isinstance(metrics_by_suffix, dict):
                for _, metrics in metrics_by_suffix.items():
                    for metric_name, value in metrics.items():
                        if isinstance(value, (int, float)):
                            if metric_name not in avg_metrics:
                                avg_metrics[metric_name] = []
                            avg_metrics[metric_name].append(value)
    else:
        # Flat structure: metrics_all[pred_path]
        for pred_path, metrics in metrics_all.items():
            if isinstance(metrics, dict):
                for metric_name, value in metrics.items():
                    if isinstance(value, (int, float)):
                        if metric_name not in avg_metrics:
                            avg_metrics[metric_name] = []
                        avg_metrics[metric_name].append(value)

    # Compute averages
    avg_results = {
        metric: float(np.mean(values)) for metric, values in avg_metrics.items()
    }
    metrics_all["average"] = avg_results

    # Convert to serializable format
    serializable_metrics = convert_to_json_serializable(metrics_all)

    # Save results
    if instance_mode:
        output_file = os.path.join(pred_dir, f"instance_metrics_{split}.json")
    else:
        output_file = os.path.join(pred_dir, f"metrics_{split}.json")

    with open(output_file, "w") as f:
        json.dump(serializable_metrics, f, indent=2)

    logger.info(f"Results saved to {output_file}")
    logger.info("Average metrics:")
    for metric, value in avg_results.items():
        logger.info(f"  {metric}: {value:.4f}")


if __name__ == "__main__":
    main()
