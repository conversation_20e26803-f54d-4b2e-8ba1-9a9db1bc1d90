"""
Universal Baseline Framework for 3D Segmentation.

This package provides a unified interface for running various baseline 
segmentation methods on 3D volumes with automatic chunking, format conversion,
and result merging.
"""

from .base_processor import BaseProcessor
from .processor_factory import ProcessorFactory, run_baseline
from .cellpose_processor import CellposeProcessor
from .micro_sam_processor import MicroSAMProcessor

__version__ = "1.0.0"
__author__ = "3D Segmentation Team"

__all__ = [
    'BaseProcessor',
    'ProcessorFactory', 
    'run_baseline',
    'CellposeProcessor',
    'MicroSAMProcessor'
]
