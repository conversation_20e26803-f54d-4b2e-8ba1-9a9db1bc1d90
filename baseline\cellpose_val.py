import os
import hydra
import numpy as np
from tqdm import tqdm
from cellpose import models, core, io

from dataprocess.volume import Volume


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    em_dir = os.path.join(cfg.datasets_root, "em_s0/val")
    output_dir = os.path.join(cfg.output_root, "baseline/cellpose")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Setup logger
    io.logger_setup()
    
    # Check GPU availability
    if not core.use_gpu():
        print("Warning: No GPU detected, using CPU")
    
    # Initialize Cellpose model
    model = models.CellposeModel(gpu=core.use_gpu())
    
    for file in tqdm(os.listdir(em_dir)):
        if file.endswith(".zst"):
            # Load volume
            em = Volume(os.path.join(em_dir, file))
            em.load()
            
            # Run Cellpose 3D segmentation
            # Method 1: 3D flows computation
            masks, flows, _ = model.eval(
                em.volume, 
                z_axis=0, 
                channel_axis=None,  # grayscale
                batch_size=32,
                do_3D=True, 
                flow3D_smooth=1,
                niter=1000
            )
            
            # Save results
            mask_volume = Volume(None)
            mask_volume.volume = masks.astype(np.uint16)
            mask_volume.save_volume(os.path.join(output_dir, file))


if __name__ == "__main__":
    main()